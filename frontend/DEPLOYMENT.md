# Frontend Deployment Guide

## Overview
This Vue.js 3 frontend is designed to work with the Express.js backend API and can be deployed to various cloud platforms.

## Environment Configuration

### Local Development
1. Copy `.env.local` and update the API URL:
```bash
VITE_API_BASE_URL=http://localhost:3000/api
```

### Production
1. Update `.env.production` with your production API URL:
```bash
VITE_API_BASE_URL=https://your-backend-domain.com/api
```

## Deployment Options

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard:
   - `VITE_API_BASE_URL`: Your backend API URL
3. Deploy automatically on push to main branch

### Netlify
1. Connect your GitHub repository to Netlify
2. Build command: `npm run build`
3. Publish directory: `dist`
4. Set environment variables in Netlify dashboard

### Manual Build
```bash
# Install dependencies
npm install

# Build for production
npm run build

# The dist/ folder contains the built application
```

## Backend Integration
- Ensure your backend API supports CORS for your frontend domain
- Update backend FRONTEND_URL environment variable
- Verify all API endpoints are accessible from your frontend domain

## Features Included
- JWT Authentication with automatic token refresh
- Role-based access control (Admin, Manager, User)
- Email verification flow
- Task management with Gantt chart visualization
- User management (Admin only)
- Responsive design with dark/light theme
- Mobile-first approach

## Testing
```bash
# Run development server
npm run dev

# Run tests
npm run test
```

## Troubleshooting
- Check browser console for API connection errors
- Verify environment variables are set correctly
- Ensure backend is running and accessible
- Check CORS configuration on backend
