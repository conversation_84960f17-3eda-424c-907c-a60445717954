/**
 * Login Performance Test Script
 * Run this in the browser console to test login performance
 */

// Test configuration
const TEST_CONFIG = {
  email: '<EMAIL>',
  password: 'pass1234',
  iterations: 5,
  timeout: 10000 // 10 seconds timeout
};

// Performance metrics
let metrics = {
  totalRequests: 0,
  successfulLogins: 0,
  failedLogins: 0,
  averageResponseTime: 0,
  responseTimes: [],
  errors: []
};

/**
 * Test login performance
 */
async function testLoginPerformance() {
  console.log('🚀 Starting login performance test...');
  console.log(`Testing with ${TEST_CONFIG.iterations} iterations`);
  
  for (let i = 0; i < TEST_CONFIG.iterations; i++) {
    console.log(`\n📊 Test iteration ${i + 1}/${TEST_CONFIG.iterations}`);
    
    try {
      const startTime = performance.now();
      
      // Clear any existing auth data
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      
      // Perform login
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: TEST_CONFIG.email,
          password: TEST_CONFIG.password
        })
      });
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      metrics.totalRequests++;
      metrics.responseTimes.push(responseTime);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          metrics.successfulLogins++;
          console.log(`✅ Login successful in ${responseTime.toFixed(2)}ms`);
        } else {
          metrics.failedLogins++;
          metrics.errors.push(`Login failed: ${data.message}`);
          console.log(`❌ Login failed: ${data.message}`);
        }
      } else {
        metrics.failedLogins++;
        const errorText = await response.text();
        metrics.errors.push(`HTTP ${response.status}: ${errorText}`);
        console.log(`❌ HTTP error ${response.status}: ${errorText}`);
      }
      
      // Wait between requests to avoid rate limiting
      if (i < TEST_CONFIG.iterations - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (error) {
      metrics.failedLogins++;
      metrics.errors.push(error.message);
      console.log(`❌ Request failed: ${error.message}`);
    }
  }
  
  // Calculate average response time
  if (metrics.responseTimes.length > 0) {
    metrics.averageResponseTime = metrics.responseTimes.reduce((a, b) => a + b, 0) / metrics.responseTimes.length;
  }
  
  // Display results
  displayResults();
}

/**
 * Display test results
 */
function displayResults() {
  console.log('\n📈 LOGIN PERFORMANCE TEST RESULTS');
  console.log('=====================================');
  console.log(`Total Requests: ${metrics.totalRequests}`);
  console.log(`Successful Logins: ${metrics.successfulLogins}`);
  console.log(`Failed Logins: ${metrics.failedLogins}`);
  console.log(`Success Rate: ${((metrics.successfulLogins / metrics.totalRequests) * 100).toFixed(2)}%`);
  console.log(`Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`);
  console.log(`Min Response Time: ${Math.min(...metrics.responseTimes).toFixed(2)}ms`);
  console.log(`Max Response Time: ${Math.max(...metrics.responseTimes).toFixed(2)}ms`);
  
  if (metrics.errors.length > 0) {
    console.log('\n❌ ERRORS:');
    metrics.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  // Performance assessment
  console.log('\n🎯 PERFORMANCE ASSESSMENT:');
  if (metrics.averageResponseTime < 500) {
    console.log('✅ Excellent performance (< 500ms)');
  } else if (metrics.averageResponseTime < 1000) {
    console.log('⚠️ Good performance (500-1000ms)');
  } else if (metrics.averageResponseTime < 2000) {
    console.log('⚠️ Acceptable performance (1-2s)');
  } else {
    console.log('❌ Poor performance (> 2s)');
  }
  
  if (metrics.successfulLogins === metrics.totalRequests) {
    console.log('✅ All login attempts successful');
  } else {
    console.log('❌ Some login attempts failed');
  }
}

/**
 * Test browser resource usage
 */
function testResourceUsage() {
  console.log('\n🔍 BROWSER RESOURCE USAGE TEST');
  console.log('=====================================');
  
  // Memory usage (if available)
  if (performance.memory) {
    console.log(`Used JS Heap Size: ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Total JS Heap Size: ${(performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`JS Heap Size Limit: ${(performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
  } else {
    console.log('Memory usage information not available');
  }
  
  // Performance entries
  const navigationEntries = performance.getEntriesByType('navigation');
  if (navigationEntries.length > 0) {
    const nav = navigationEntries[0];
    console.log(`Page Load Time: ${nav.loadEventEnd - nav.navigationStart}ms`);
    console.log(`DOM Content Loaded: ${nav.domContentLoadedEventEnd - nav.navigationStart}ms`);
  }
}

// Export functions for manual testing
window.testLoginPerformance = testLoginPerformance;
window.testResourceUsage = testResourceUsage;

console.log('🧪 Login performance test script loaded!');
console.log('Run testLoginPerformance() to start the test');
console.log('Run testResourceUsage() to check browser resource usage');
