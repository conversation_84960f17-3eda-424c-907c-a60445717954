#!/bin/bash

# Task Management System Database Setup Script
# This script sets up the PostgreSQL database with all tables and sample data

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default database configuration
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-taskmanagement}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-}

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if PostgreSQL is running
check_postgres() {
    print_status "Checking PostgreSQL connection..."
    
    if command -v psql >/dev/null 2>&1; then
        if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c '\q' >/dev/null 2>&1; then
            print_success "PostgreSQL is running and accessible"
            return 0
        else
            print_error "Cannot connect to PostgreSQL. Please check your connection settings."
            return 1
        fi
    else
        print_error "psql command not found. Please install PostgreSQL client."
        return 1
    fi
}

# Function to create database if it doesn't exist
create_database() {
    print_status "Creating database '$DB_NAME' if it doesn't exist..."
    
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "
        SELECT 'CREATE DATABASE $DB_NAME' 
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\gexec
    " >/dev/null 2>&1
    
    print_success "Database '$DB_NAME' is ready"
}

# Function to run the SQL setup file
setup_tables() {
    print_status "Setting up database tables and data..."
    
    if [ ! -f "database_setup.sql" ]; then
        print_error "database_setup.sql file not found in current directory"
        exit 1
    fi
    
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f database_setup.sql
    
    if [ $? -eq 0 ]; then
        print_success "Database setup completed successfully!"
    else
        print_error "Database setup failed"
        exit 1
    fi
}

# Function to verify the setup
verify_setup() {
    print_status "Verifying database setup..."
    
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
        SELECT 'Users:' as table_name, COUNT(*) as count FROM users
        UNION ALL
        SELECT 'Tasks:', COUNT(*) FROM tasks
        UNION ALL
        SELECT 'Roles:', COUNT(*) FROM roles
        UNION ALL
        SELECT 'Assignments:', COUNT(*) FROM task_assigned_users;
    "
}

# Main execution
main() {
    echo "=================================================="
    echo "  Task Management System Database Setup"
    echo "=================================================="
    echo ""
    echo "Database Configuration:"
    echo "  Host: $DB_HOST"
    echo "  Port: $DB_PORT"
    echo "  Database: $DB_NAME"
    echo "  User: $DB_USER"
    echo ""
    
    # Check if password is provided
    if [ -z "$DB_PASSWORD" ]; then
        print_warning "No password provided. You may be prompted for password."
    fi
    
    # Run setup steps
    if check_postgres; then
        create_database
        setup_tables
        verify_setup
        
        echo ""
        echo "=================================================="
        print_success "Setup completed successfully!"
        echo "=================================================="
        echo ""
        echo "You can now start your application with these test accounts:"
        echo ""
        echo "🔑 Admin Account:"
        echo "   Email: <EMAIL>"
        echo "   Password: password123"
        echo ""
        echo "👨‍💼 Manager Account:"
        echo "   Email: <EMAIL>"
        echo "   Password: password123"
        echo ""
        echo "👤 User Account:"
        echo "   Email: <EMAIL>"
        echo "   Password: password123"
        echo ""
        echo "📊 The database includes:"
        echo "   • 12 users with different roles"
        echo "   • 23 tasks with various statuses and priorities"
        echo "   • Realistic task assignments"
        echo "   • Useful views and functions"
        echo ""
        echo "🚀 Start your backend server and begin testing!"
        
    else
        print_error "Setup failed. Please check your PostgreSQL installation and configuration."
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Environment variables:"
        echo "  DB_HOST     Database host (default: localhost)"
        echo "  DB_PORT     Database port (default: 5432)"
        echo "  DB_NAME     Database name (default: taskmanagement)"
        echo "  DB_USER     Database user (default: postgres)"
        echo "  DB_PASSWORD Database password (default: empty)"
        echo ""
        echo "Example:"
        echo "  DB_PASSWORD=mypassword ./setup_database.sh"
        echo "  DB_HOST=remote-host DB_USER=myuser ./setup_database.sh"
        exit 0
        ;;
    *)
        main
        ;;
esac
