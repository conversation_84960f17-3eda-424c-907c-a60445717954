-- Task Management System Database Setup
-- This file creates all tables and populates them with robust test data
-- Run this file to get up and running quickly

-- Drop existing tables if they exist (in correct order due to foreign keys)
DROP TABLE IF EXISTS task_assigned_users CASCADE;
DROP TABLE IF EXISTS tasks CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS roles CASCADE;

-- Create roles table
CREATE TABLE roles (
  role_id SERIAL PRIMARY KEY,
  role_name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create users table
CREATE TABLE users (
  user_id SERIAL PRIMARY KEY,
  username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  middle_and_other_name VARCHAR(100),
  last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  role_id INTEGER REFERENCES roles(role_id) ON DELETE SET NULL,
  is_verified BOOLEAN DEFAULT FALSE,
  verification_token VARCHAR(255),
  verification_token_expires TIMESTAMP WITH TIME ZONE,
  reset_password_token VARCHAR(255),
  reset_password_expires TIMESTAMP WITH TIME ZONE,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tasks table
CREATE TABLE tasks (
  task_id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  deadline TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'inprogress', 'completed', 'archived')),
  priority INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create task_assigned_users junction table
CREATE TABLE task_assigned_users (
  task_id INTEGER REFERENCES tasks(task_id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (task_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role_id ON users(role_id);
CREATE INDEX idx_tasks_created_by ON tasks(created_by);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_deadline ON tasks(deadline);
CREATE INDEX idx_task_assigned_users_task_id ON task_assigned_users(task_id);
CREATE INDEX idx_task_assigned_users_user_id ON task_assigned_users(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert roles
INSERT INTO roles (role_name, description) VALUES
('admin', 'Full system access - can manage users, tasks, and system settings'),
('manager', 'Can create and assign tasks, manage team members'),
('user', 'Can view assigned tasks and update their status');

-- Insert users (passwords are hashed version of 'password123')
-- Note: In production, use proper password hashing
INSERT INTO users (username, email, password_hash, first_name, last_name, role_id, is_verified) VALUES
('admin', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'System', 'Administrator', 1, true),
('jdoe', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'John', 'Doe', 2, true),
('asmith', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Alice', 'Smith', 2, true),
('bwilson', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Bob', 'Wilson', 3, true),
('cjohnson', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Carol', 'Johnson', 3, true),
('dlee', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'David', 'Lee', 3, true),
('ebrown', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Emma', 'Brown', 3, true),
('fgarcia', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Frank', 'Garcia', 3, true),
('gmiller', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Grace', 'Miller', 3, false),
('hwilliams', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Henry', 'Williams', 3, true);

-- Insert comprehensive task data
INSERT INTO tasks (title, description, created_by, deadline, status, priority, created_at) VALUES
-- High Priority Tasks
('Setup Development Environment', 'Configure development tools, IDE settings, and local database for the new team members', 1, NOW() + INTERVAL '3 days', 'pending', 4, NOW() - INTERVAL '1 day'),
('Security Audit Implementation', 'Conduct comprehensive security audit and implement recommended fixes for user authentication system', 2, NOW() + INTERVAL '5 days', 'inprogress', 4, NOW() - INTERVAL '2 days'),
('Database Migration Script', 'Create and test database migration scripts for production deployment', 1, NOW() + INTERVAL '7 days', 'pending', 4, NOW() - INTERVAL '1 day'),

-- Medium-High Priority Tasks
('API Documentation Update', 'Update REST API documentation with new endpoints and authentication methods', 2, NOW() + INTERVAL '10 days', 'inprogress', 3, NOW() - INTERVAL '3 days'),
('User Interface Redesign', 'Redesign the main dashboard interface based on user feedback and usability testing', 3, NOW() + INTERVAL '14 days', 'pending', 3, NOW() - INTERVAL '1 day'),
('Performance Optimization', 'Optimize database queries and implement caching strategies for better application performance', 2, NOW() + INTERVAL '12 days', 'inprogress', 3, NOW() - INTERVAL '4 days'),
('Mobile App Testing', 'Comprehensive testing of mobile application across different devices and operating systems', 3, NOW() + INTERVAL '8 days', 'pending', 3, NOW() - INTERVAL '2 days'),

-- Medium Priority Tasks
('Code Review Process', 'Establish and document code review guidelines and implement automated code quality checks', 1, NOW() + INTERVAL '15 days', 'pending', 2, NOW() - INTERVAL '1 day'),
('User Training Materials', 'Create comprehensive user training materials including video tutorials and documentation', 3, NOW() + INTERVAL '20 days', 'pending', 2, NOW() - INTERVAL '2 days'),
('Backup System Setup', 'Implement automated backup system for database and application files', 2, NOW() + INTERVAL '18 days', 'inprogress', 2, NOW() - INTERVAL '5 days'),
('Email Notification System', 'Develop and integrate email notification system for task assignments and updates', 1, NOW() + INTERVAL '16 days', 'pending', 2, NOW() - INTERVAL '1 day'),
('Report Generation Feature', 'Build comprehensive reporting system with charts and export functionality', 2, NOW() + INTERVAL '25 days', 'pending', 2, NOW() - INTERVAL '3 days'),

-- Low Priority Tasks
('UI Theme Customization', 'Allow users to customize application themes and color schemes', 3, NOW() + INTERVAL '30 days', 'pending', 1, NOW() - INTERVAL '1 day'),
('Social Media Integration', 'Integrate social media sharing capabilities for project updates', 2, NOW() + INTERVAL '35 days', 'pending', 1, NOW() - INTERVAL '2 days'),
('Advanced Search Features', 'Implement advanced search and filtering options for tasks and users', 1, NOW() + INTERVAL '28 days', 'pending', 1, NOW() - INTERVAL '1 day'),

-- Completed Tasks
('Initial Project Setup', 'Set up project repository, basic folder structure, and initial configuration files', 1, NOW() - INTERVAL '10 days', 'completed', 3, NOW() - INTERVAL '15 days'),
('Database Schema Design', 'Design and implement the initial database schema for users, tasks, and roles', 1, NOW() - INTERVAL '8 days', 'completed', 4, NOW() - INTERVAL '12 days'),
('Basic Authentication System', 'Implement user registration, login, and JWT token-based authentication', 2, NOW() - INTERVAL '5 days', 'completed', 4, NOW() - INTERVAL '10 days'),
('Task CRUD Operations', 'Develop create, read, update, and delete operations for task management', 2, NOW() - INTERVAL '3 days', 'completed', 3, NOW() - INTERVAL '8 days'),
('User Role Management', 'Implement role-based access control system with admin, manager, and user roles', 1, NOW() - INTERVAL '2 days', 'completed', 3, NOW() - INTERVAL '7 days'),

-- Archived Tasks
('Legacy System Analysis', 'Analyze the old task management system for data migration requirements', 1, NOW() - INTERVAL '20 days', 'archived', 2, NOW() - INTERVAL '25 days'),
('Prototype Development', 'Create initial prototype for stakeholder review and feedback', 2, NOW() - INTERVAL '18 days', 'archived', 2, NOW() - INTERVAL '22 days');

-- Insert task assignments
INSERT INTO task_assigned_users (task_id, user_id, assigned_at) VALUES
-- High priority tasks assignments
(1, 4, NOW() - INTERVAL '1 day'),  -- Setup Development Environment -> Bob Wilson
(1, 5, NOW() - INTERVAL '1 day'),  -- Setup Development Environment -> Carol Johnson
(2, 6, NOW() - INTERVAL '2 days'), -- Security Audit -> David Lee
(2, 7, NOW() - INTERVAL '2 days'), -- Security Audit -> Emma Brown
(3, 4, NOW() - INTERVAL '1 day'),  -- Database Migration -> Bob Wilson

-- Medium-high priority assignments
(4, 5, NOW() - INTERVAL '3 days'), -- API Documentation -> Carol Johnson
(4, 8, NOW() - INTERVAL '3 days'), -- API Documentation -> Frank Garcia
(5, 6, NOW() - INTERVAL '1 day'),  -- UI Redesign -> David Lee
(5, 7, NOW() - INTERVAL '1 day'),  -- UI Redesign -> Emma Brown
(6, 4, NOW() - INTERVAL '4 days'), -- Performance Optimization -> Bob Wilson
(7, 5, NOW() - INTERVAL '2 days'), -- Mobile App Testing -> Carol Johnson
(7, 10, NOW() - INTERVAL '2 days'), -- Mobile App Testing -> Henry Williams

-- Medium priority assignments
(8, 6, NOW() - INTERVAL '1 day'),  -- Code Review Process -> David Lee
(9, 7, NOW() - INTERVAL '2 days'), -- User Training -> Emma Brown
(9, 8, NOW() - INTERVAL '2 days'), -- User Training -> Frank Garcia
(10, 4, NOW() - INTERVAL '5 days'), -- Backup System -> Bob Wilson
(11, 5, NOW() - INTERVAL '1 day'),  -- Email Notifications -> Carol Johnson
(12, 6, NOW() - INTERVAL '3 days'), -- Report Generation -> David Lee
(12, 10, NOW() - INTERVAL '3 days'), -- Report Generation -> Henry Williams

-- Low priority assignments
(13, 7, NOW() - INTERVAL '1 day'),  -- UI Theme Customization -> Emma Brown
(14, 8, NOW() - INTERVAL '2 days'), -- Social Media Integration -> Frank Garcia
(15, 10, NOW() - INTERVAL '1 day'), -- Advanced Search -> Henry Williams

-- Completed task assignments (historical data)
(16, 4, NOW() - INTERVAL '15 days'), -- Initial Project Setup -> Bob Wilson
(17, 5, NOW() - INTERVAL '12 days'), -- Database Schema -> Carol Johnson
(18, 6, NOW() - INTERVAL '10 days'), -- Authentication System -> David Lee
(19, 7, NOW() - INTERVAL '8 days'),  -- Task CRUD -> Emma Brown
(20, 4, NOW() - INTERVAL '7 days'),  -- User Role Management -> Bob Wilson

-- Some tasks with multiple assignees for collaboration
(6, 10, NOW() - INTERVAL '4 days'), -- Performance Optimization -> Henry Williams (additional)
(12, 8, NOW() - INTERVAL '3 days'); -- Report Generation -> Frank Garcia (additional)

-- Create some useful views for common queries
CREATE OR REPLACE VIEW user_task_summary AS
SELECT
    u.user_id,
    u.username,
    u.first_name,
    u.last_name,
    u.email,
    r.role_name,
    COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_tasks,
    COUNT(CASE WHEN t.status = 'inprogress' THEN 1 END) as inprogress_tasks,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
    COUNT(tau.task_id) as total_assigned_tasks
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
LEFT JOIN task_assigned_users tau ON u.user_id = tau.user_id
LEFT JOIN tasks t ON tau.task_id = t.task_id
GROUP BY u.user_id, u.username, u.first_name, u.last_name, u.email, r.role_name
ORDER BY u.username;

CREATE OR REPLACE VIEW task_details_view AS
SELECT
    t.task_id,
    t.title,
    t.description,
    t.status,
    t.priority,
    t.deadline,
    t.created_at,
    t.updated_at,
    creator.first_name || ' ' || creator.last_name as created_by_name,
    creator.username as created_by_username,
    STRING_AGG(assignee.first_name || ' ' || assignee.last_name, ', ') as assigned_to_names,
    COUNT(tau.user_id) as assignee_count
FROM tasks t
LEFT JOIN users creator ON t.created_by = creator.user_id
LEFT JOIN task_assigned_users tau ON t.task_id = tau.task_id
LEFT JOIN users assignee ON tau.user_id = assignee.user_id
GROUP BY t.task_id, t.title, t.description, t.status, t.priority, t.deadline,
         t.created_at, t.updated_at, creator.first_name, creator.last_name, creator.username
ORDER BY t.priority DESC, t.deadline ASC NULLS LAST;

-- Create a function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats(user_id_param INTEGER)
RETURNS TABLE (
    total_tasks BIGINT,
    pending_tasks BIGINT,
    inprogress_tasks BIGINT,
    completed_tasks BIGINT,
    overdue_tasks BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_tasks,
        COUNT(CASE WHEN t.status = 'inprogress' THEN 1 END) as inprogress_tasks,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN t.deadline < NOW() AND t.status NOT IN ('completed', 'archived') THEN 1 END) as overdue_tasks
    FROM task_assigned_users tau
    JOIN tasks t ON tau.task_id = t.task_id
    WHERE tau.user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get task statistics by priority
CREATE OR REPLACE FUNCTION get_priority_stats()
RETURNS TABLE (
    priority INTEGER,
    priority_name TEXT,
    task_count BIGINT,
    completed_count BIGINT,
    pending_count BIGINT,
    inprogress_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.priority,
        CASE
            WHEN t.priority = 1 THEN 'Low'
            WHEN t.priority = 2 THEN 'Medium'
            WHEN t.priority = 3 THEN 'High'
            WHEN t.priority = 4 THEN 'Urgent'
            ELSE 'Unknown'
        END as priority_name,
        COUNT(*) as task_count,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN t.status = 'inprogress' THEN 1 END) as inprogress_count
    FROM tasks t
    GROUP BY t.priority
    ORDER BY t.priority DESC;
END;
$$ LANGUAGE plpgsql;

-- Insert some additional sample data for testing edge cases
INSERT INTO users (username, email, password_hash, first_name, last_name, role_id, is_verified) VALUES
('testuser1', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Test', 'User One', 3, false),
('testuser2', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Test', 'User Two', 3, true);

-- Insert some tasks without assignments (unassigned tasks)
INSERT INTO tasks (title, description, created_by, deadline, status, priority) VALUES
('Unassigned High Priority Task', 'This task needs to be assigned to someone urgently', 1, NOW() + INTERVAL '2 days', 'pending', 4),
('Future Planning Task', 'Long-term planning task for next quarter', 2, NOW() + INTERVAL '60 days', 'pending', 1),
('Overdue Task Example', 'This task is overdue for testing purposes', 1, NOW() - INTERVAL '5 days', 'pending', 3);

-- Final data verification queries (commented out - uncomment to run)
/*
-- Verify the setup
SELECT 'Users created:' as info, COUNT(*) as count FROM users
UNION ALL
SELECT 'Tasks created:', COUNT(*) FROM tasks
UNION ALL
SELECT 'Task assignments:', COUNT(*) FROM task_assigned_users
UNION ALL
SELECT 'Roles created:', COUNT(*) FROM roles;

-- Show user summary
SELECT * FROM user_task_summary;

-- Show task details
SELECT task_id, title, status, priority, assigned_to_names, assignee_count
FROM task_details_view
ORDER BY priority DESC, deadline ASC NULLS LAST;

-- Show priority statistics
SELECT * FROM get_priority_stats();

-- Show stats for a specific user (user_id = 4)
SELECT * FROM get_user_stats(4);
*/

-- Success message
SELECT 'Database setup completed successfully! You now have:' as message
UNION ALL SELECT '- 3 roles (admin, manager, user)'
UNION ALL SELECT '- 12 users with various roles and verification status'
UNION ALL SELECT '- 23 tasks with different priorities and statuses'
UNION ALL SELECT '- Realistic task assignments'
UNION ALL SELECT '- Useful views and functions for reporting'
UNION ALL SELECT '- Proper indexes for performance'
UNION ALL SELECT ''
UNION ALL SELECT 'Default login credentials:'
UNION ALL SELECT 'Admin: <EMAIL> / password123'
UNION ALL SELECT 'Manager: <EMAIL> / password123'
UNION ALL SELECT 'User: <EMAIL> / password123';
