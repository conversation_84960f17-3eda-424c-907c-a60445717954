-- Cytonn Investment & Audit Firm - Task Management System Database Setup
-- This file creates all tables and populates them with robust Kenyan-themed test data
-- Focused on investment and audit firm operations
-- Run this file to get up and running quickly

-- Drop existing tables if they exist (in correct order due to foreign keys)
DROP TABLE IF EXISTS email_verification_tokens CASCADE;
DROP TABLE IF EXISTS refresh_tokens CASCADE;
DROP TABLE IF EXISTS token_blacklist CASCADE;
DROP TABLE IF EXISTS task_assigned_users CASCADE;
DROP TABLE IF EXISTS tasks CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS roles CASCADE;

-- Create roles table
CREATE TABLE roles (
  role_id SERIAL PRIMARY KEY,
  role_name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create users table (matching existing schema)
CREATE TABLE users (
  user_id SERIAL PRIMARY KEY,
  username VARCHA<PERSON>(255) NOT NULL UNIQUE,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
  middle_and_other_name VARCHAR(100),
  last_name VARCHAR(100) NOT NULL,
  role_id INTEGER NOT NULL DEFAULT 2,
  status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'deleted')),
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tasks table
CREATE TABLE tasks (
  task_id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  deadline TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'inprogress', 'completed', 'archived')),
  priority INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create task_assigned_users junction table
CREATE TABLE task_assigned_users (
  task_id INTEGER REFERENCES tasks(task_id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  PRIMARY KEY (task_id, user_id)
);

-- Create token blacklist table
CREATE TABLE token_blacklist (
  id SERIAL PRIMARY KEY,
  jti VARCHAR(255) UNIQUE NOT NULL,
  token_hash VARCHAR(255) NOT NULL,
  user_id INTEGER REFERENCES users(user_id),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  revoked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reason VARCHAR(50) DEFAULT 'logout' CHECK (reason IN ('logout', 'forced_logout', 'security_breach'))
);

-- Create refresh tokens table
CREATE TABLE refresh_tokens (
  id SERIAL PRIMARY KEY,
  jti VARCHAR(255) UNIQUE NOT NULL,
  user_id INTEGER NOT NULL REFERENCES users(user_id),
  device_info JSONB,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_revoked BOOLEAN DEFAULT FALSE
);

-- Create email verification tokens table
CREATE TABLE email_verification_tokens (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(user_id),
  token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  used_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_is_verified ON users(is_verified);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);
CREATE INDEX IF NOT EXISTS idx_tasks_created_by ON tasks(created_by);
CREATE INDEX IF NOT EXISTS idx_tasks_deadline ON tasks(deadline);
CREATE INDEX IF NOT EXISTS idx_task_assigned_users_task ON task_assigned_users(task_id);
CREATE INDEX IF NOT EXISTS idx_task_assigned_users_user ON task_assigned_users(user_id);
CREATE INDEX IF NOT EXISTS idx_token_blacklist_jti ON token_blacklist(jti);
CREATE INDEX IF NOT EXISTS idx_token_blacklist_expires ON token_blacklist(expires_at);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_jti ON refresh_tokens(jti);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user ON refresh_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_expires ON refresh_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user ON email_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token ON email_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires ON email_verification_tokens(expires_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert roles specific to investment/audit firm
INSERT INTO roles (role_id, role_name, description) VALUES
(1, 'Admin', 'Full system access - can manage users, tasks, and system settings'),
(2, 'Manager', 'Senior Partners and Department Heads - can create and assign tasks, manage team members'),
(3, 'User', 'Analysts, Associates, and Junior Staff - can view assigned tasks and update their status')
ON CONFLICT (role_id) DO NOTHING;

-- Insert users with Kenyan names and investment/audit firm context
-- Passwords are hashed version of 'password123' using bcrypt
INSERT INTO users (username, email, password_hash, first_name, middle_and_other_name, last_name, role_id, is_verified) VALUES
-- Senior Management & Partners
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'System', '', 'Administrator', 1, true),
('kimeudom', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Dominic', 'Kiio', 'Kimeu', 1, true),
('edwin_dande', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Edwin', 'Mwangi', 'Dande', 2, true),
('shiv_arora', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Shiv', 'Kumar', 'Arora', 2, true),

-- Department Heads & Senior Managers
('mary_njeri', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Mary', 'Wambui', 'Njeri', 2, true),
('james_mwangi', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'James', 'Mwangi', 'Njoroge', 2, true),
('grace_wanjiku', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Grace', 'Wanjiku', 'Kamau', 2, true),

-- Senior Analysts & Associates
('peter_muthomi', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Peter', 'Muthumi', 'Muthomi', 3, true),
('faith_wanjiru', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Faith', 'Wanjiru', 'Kariuki', 3, true),
('samuel_kiprotich', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Samuel', 'Kiprotich', 'Kemboi', 3, true),
('esther_nyambura', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Esther', 'Nyambura', 'Githinji', 3, true),
('david_otieno', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'David', 'Otieno', 'Ochieng', 3, true),
('mercy_akinyi', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Mercy', 'Akinyi', 'Ouma', 3, true),
('john_mutua', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'John', 'Mutua', 'Musyoka', 3, false),
('caroline_chebet', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Caroline', 'Chebet', 'Ruto', 3, true)
ON CONFLICT (email) DO NOTHING;

-- Insert comprehensive investment & audit firm task data
INSERT INTO tasks (title, description, created_by, deadline, status, priority, created_at) VALUES
-- Urgent Investment & Audit Tasks (Priority 4)
('Safaricom PLC Annual Audit', 'Complete the annual financial audit for Safaricom PLC including revenue recognition, asset valuation, and regulatory compliance review', 3, NOW() + INTERVAL '5 days', 'inprogress', 4, NOW() - INTERVAL '2 days'),
('KCB Group Investment Due Diligence', 'Conduct comprehensive due diligence for KCB Group''s proposed acquisition of a fintech startup', 4, NOW() + INTERVAL '7 days', 'pending', 4, NOW() - INTERVAL '1 day'),
('NSE Listed Company Compliance Review', 'Review compliance with Capital Markets Authority regulations for newly listed company', 5, NOW() + INTERVAL '3 days', 'inprogress', 4, NOW() - INTERVAL '3 days'),

-- High Priority Tasks (Priority 3)
('Equity Bank Risk Assessment', 'Perform comprehensive risk assessment for Equity Bank''s expansion into DRC market', 6, NOW() + INTERVAL '10 days', 'pending', 3, NOW() - INTERVAL '1 day'),
('EABL Financial Statement Analysis', 'Analyze East African Breweries Limited Q3 financial statements and prepare investment recommendation', 7, NOW() + INTERVAL '12 days', 'inprogress', 3, NOW() - INTERVAL '4 days'),
('Centum Investment Portfolio Review', 'Review and analyze Centum Investment Company''s real estate portfolio performance', 3, NOW() + INTERVAL '14 days', 'pending', 3, NOW() - INTERVAL '2 days'),
('Co-operative Bank Audit Planning', 'Develop comprehensive audit plan for Co-operative Bank of Kenya annual audit', 4, NOW() + INTERVAL '8 days', 'inprogress', 3, NOW() - INTERVAL '5 days'),

-- Medium Priority Tasks (Priority 2)
('Kenya Airways Restructuring Analysis', 'Analyze Kenya Airways debt restructuring proposal and assess viability', 5, NOW() + INTERVAL '18 days', 'pending', 2, NOW() - INTERVAL '1 day'),
('Bamburi Cement ESG Audit', 'Conduct Environmental, Social, and Governance audit for Bamburi Cement Company', 6, NOW() + INTERVAL '20 days', 'pending', 2, NOW() - INTERVAL '2 days'),
('NCBA Group Digital Transformation Review', 'Review NCBA Group''s digital transformation strategy and technology investments', 7, NOW() + INTERVAL '16 days', 'inprogress', 2, NOW() - INTERVAL '6 days'),
('Britam Holdings Insurance Audit', 'Complete statutory audit of Britam Holdings insurance operations', 3, NOW() + INTERVAL '22 days', 'pending', 2, NOW() - INTERVAL '1 day'),
('Nairobi Securities Exchange Market Analysis', 'Prepare comprehensive market analysis report for NSE performance trends', 4, NOW() + INTERVAL '25 days', 'pending', 2, NOW() - INTERVAL '3 days'),

-- Low Priority Tasks (Priority 1)
('Staff Training on IFRS 17', 'Organize training sessions for audit staff on new IFRS 17 insurance contracts standard', 5, NOW() + INTERVAL '30 days', 'pending', 1, NOW() - INTERVAL '1 day'),
('Office Automation System Upgrade', 'Upgrade office automation systems and implement new document management solution', 6, NOW() + INTERVAL '35 days', 'pending', 1, NOW() - INTERVAL '2 days'),
('Client Relationship Management Enhancement', 'Enhance CRM system to better track client interactions and audit progress', 7, NOW() + INTERVAL '28 days', 'pending', 1, NOW() - INTERVAL '1 day'),

-- Completed Tasks
('Tusker Mattresses Limited Audit', 'Completed annual audit of Tusker Mattresses Limited manufacturing operations', 3, NOW() - INTERVAL '5 days', 'completed', 3, NOW() - INTERVAL '15 days'),
('Diamond Trust Bank Investment Analysis', 'Completed investment analysis for Diamond Trust Bank''s expansion strategy', 4, NOW() - INTERVAL '8 days', 'completed', 4, NOW() - INTERVAL '12 days'),
('Unga Group Financial Review', 'Completed financial review and audit of Unga Group Limited operations', 5, NOW() - INTERVAL '3 days', 'completed', 3, NOW() - INTERVAL '10 days'),
('Standard Chartered Bank Compliance Audit', 'Completed regulatory compliance audit for Standard Chartered Bank Kenya', 6, NOW() - INTERVAL '2 days', 'completed', 4, NOW() - INTERVAL '8 days'),
('I&M Bank Technology Risk Assessment', 'Completed technology and cybersecurity risk assessment for I&M Bank', 7, NOW() - INTERVAL '1 day', 'completed', 3, NOW() - INTERVAL '7 days'),

-- Archived Tasks
('Legacy Audit Software Migration', 'Migrated from legacy audit software to new cloud-based solution', 2, NOW() - INTERVAL '20 days', 'archived', 2, NOW() - INTERVAL '25 days'),
('Old Client File Digitization', 'Digitized historical client files for better accessibility and compliance', 3, NOW() - INTERVAL '18 days', 'archived', 1, NOW() - INTERVAL '22 days');

-- Insert task assignments for investment & audit firm
INSERT INTO task_assigned_users (task_id, user_id) VALUES
-- Urgent tasks assignments (Priority 4)
(1, 8),   -- Safaricom Audit -> Peter Muthomi (Senior Analyst)
(1, 9),   -- Safaricom Audit -> Faith Wanjiru (Senior Analyst)
(1, 10),  -- Safaricom Audit -> Samuel Kiprotich (Senior Analyst)
(2, 5),   -- KCB Due Diligence -> Mary Njeri (Dept Head)
(2, 11),  -- KCB Due Diligence -> Esther Nyambura (Analyst)
(3, 6),   -- NSE Compliance -> James Mwangi (Dept Head)
(3, 12),  -- NSE Compliance -> David Otieno (Analyst)

-- High priority assignments (Priority 3)
(4, 7),   -- Equity Bank Risk -> Grace Wanjiku (Dept Head)
(4, 13),  -- Equity Bank Risk -> Mercy Akinyi (Analyst)
(5, 8),   -- EABL Analysis -> Peter Muthomi (Senior Analyst)
(5, 9),   -- EABL Analysis -> Faith Wanjiru (Senior Analyst)
(6, 5),   -- Centum Portfolio -> Mary Njeri (Dept Head)
(6, 10),  -- Centum Portfolio -> Samuel Kiprotich (Senior Analyst)
(7, 6),   -- Co-op Bank Audit -> James Mwangi (Dept Head)
(7, 11),  -- Co-op Bank Audit -> Esther Nyambura (Analyst)

-- Medium priority assignments (Priority 2)
(8, 7),   -- Kenya Airways -> Grace Wanjiku (Dept Head)
(8, 12),  -- Kenya Airways -> David Otieno (Analyst)
(9, 8),   -- Bamburi ESG -> Peter Muthomi (Senior Analyst)
(9, 15),  -- Bamburi ESG -> Caroline Chebet (Analyst)
(10, 5),  -- NCBA Digital -> Mary Njeri (Dept Head)
(10, 9),  -- NCBA Digital -> Faith Wanjiru (Senior Analyst)
(11, 6),  -- Britam Audit -> James Mwangi (Dept Head)
(11, 13), -- Britam Audit -> Mercy Akinyi (Analyst)
(12, 7),  -- NSE Market Analysis -> Grace Wanjiku (Dept Head)
(12, 10), -- NSE Market Analysis -> Samuel Kiprotich (Senior Analyst)

-- Low priority assignments (Priority 1)
(13, 3),  -- IFRS Training -> Edwin Dande (Senior Manager)
(13, 8),  -- IFRS Training -> Peter Muthomi (Senior Analyst)
(14, 4),  -- Office Automation -> Shiv Arora (Senior Manager)
(14, 14), -- Office Automation -> John Mutua (Analyst)
(15, 5),  -- CRM Enhancement -> Mary Njeri (Dept Head)
(15, 11), -- CRM Enhancement -> Esther Nyambura (Analyst)

-- Completed task assignments (historical data)
(16, 8),  -- Tusker Audit -> Peter Muthomi
(16, 12), -- Tusker Audit -> David Otieno
(17, 5),  -- DTB Analysis -> Mary Njeri
(17, 9),  -- DTB Analysis -> Faith Wanjiru
(18, 6),  -- Unga Review -> James Mwangi
(18, 10), -- Unga Review -> Samuel Kiprotich
(19, 7),  -- StanChart Compliance -> Grace Wanjiku
(19, 11), -- StanChart Compliance -> Esther Nyambura
(20, 3),  -- I&M Tech Risk -> Edwin Dande
(20, 13); -- I&M Tech Risk -> Mercy Akinyi

-- Create useful views for investment & audit firm operations
CREATE OR REPLACE VIEW user_task_summary AS
SELECT
    u.user_id,
    u.username,
    u.first_name || COALESCE(' ' || u.middle_and_other_name, '') || ' ' || u.last_name as full_name,
    u.email,
    r.role_name,
    u.status as user_status,
    u.is_verified,
    COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_tasks,
    COUNT(CASE WHEN t.status = 'inprogress' THEN 1 END) as inprogress_tasks,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN t.status = 'archived' THEN 1 END) as archived_tasks,
    COUNT(tau.task_id) as total_assigned_tasks,
    COUNT(CASE WHEN t.priority = 4 THEN 1 END) as urgent_tasks,
    COUNT(CASE WHEN t.deadline < NOW() AND t.status NOT IN ('completed', 'archived') THEN 1 END) as overdue_tasks
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
LEFT JOIN task_assigned_users tau ON u.user_id = tau.user_id
LEFT JOIN tasks t ON tau.task_id = t.task_id
GROUP BY u.user_id, u.username, u.first_name, u.middle_and_other_name, u.last_name, u.email, r.role_name, u.status, u.is_verified
ORDER BY r.role_id, u.last_name, u.first_name;

CREATE OR REPLACE VIEW task_details_view AS
SELECT
    t.task_id,
    t.title,
    t.description,
    t.status,
    CASE
        WHEN t.priority = 4 THEN 'Urgent'
        WHEN t.priority = 3 THEN 'High'
        WHEN t.priority = 2 THEN 'Medium'
        WHEN t.priority = 1 THEN 'Low'
        ELSE 'Unknown'
    END as priority_label,
    t.priority,
    t.deadline,
    CASE
        WHEN t.deadline < NOW() AND t.status NOT IN ('completed', 'archived') THEN true
        ELSE false
    END as is_overdue,
    t.created_at,
    t.updated_at,
    creator.first_name || COALESCE(' ' || creator.middle_and_other_name, '') || ' ' || creator.last_name as created_by_name,
    creator.username as created_by_username,
    creator.email as created_by_email,
    STRING_AGG(assignee.first_name || COALESCE(' ' || assignee.middle_and_other_name, '') || ' ' || assignee.last_name, ', ' ORDER BY assignee.last_name) as assigned_to_names,
    STRING_AGG(assignee.email, ', ' ORDER BY assignee.last_name) as assigned_to_emails,
    COUNT(tau.user_id) as assignee_count,
    EXTRACT(DAYS FROM (t.deadline - NOW())) as days_until_deadline
FROM tasks t
LEFT JOIN users creator ON t.created_by = creator.user_id
LEFT JOIN task_assigned_users tau ON t.task_id = tau.task_id
LEFT JOIN users assignee ON tau.user_id = assignee.user_id
GROUP BY t.task_id, t.title, t.description, t.status, t.priority, t.deadline,
         t.created_at, t.updated_at, creator.first_name, creator.middle_and_other_name,
         creator.last_name, creator.username, creator.email
ORDER BY t.priority DESC, t.deadline ASC NULLS LAST;

-- Create a function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats(user_id_param INTEGER)
RETURNS TABLE (
    total_tasks BIGINT,
    pending_tasks BIGINT,
    inprogress_tasks BIGINT,
    completed_tasks BIGINT,
    overdue_tasks BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_tasks,
        COUNT(CASE WHEN t.status = 'inprogress' THEN 1 END) as inprogress_tasks,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN t.deadline < NOW() AND t.status NOT IN ('completed', 'archived') THEN 1 END) as overdue_tasks
    FROM task_assigned_users tau
    JOIN tasks t ON tau.task_id = t.task_id
    WHERE tau.user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get task statistics by priority
CREATE OR REPLACE FUNCTION get_priority_stats()
RETURNS TABLE (
    priority INTEGER,
    priority_name TEXT,
    task_count BIGINT,
    completed_count BIGINT,
    pending_count BIGINT,
    inprogress_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.priority,
        CASE
            WHEN t.priority = 1 THEN 'Low'
            WHEN t.priority = 2 THEN 'Medium'
            WHEN t.priority = 3 THEN 'High'
            WHEN t.priority = 4 THEN 'Urgent'
            ELSE 'Unknown'
        END as priority_name,
        COUNT(*) as task_count,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN t.status = 'inprogress' THEN 1 END) as inprogress_count
    FROM tasks t
    GROUP BY t.priority
    ORDER BY t.priority DESC;
END;
$$ LANGUAGE plpgsql;

-- Insert some additional sample data for testing edge cases
INSERT INTO users (username, email, password_hash, first_name, last_name, role_id, is_verified) VALUES
('testuser1', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Test', 'User One', 3, false),
('testuser2', '<EMAIL>', '$2b$10$rQZ9QmjlhQZxQmjlhQZxQOeKfKfKfKfKfKfKfKfKfKfKfKfKfKfKf', 'Test', 'User Two', 3, true);

-- Insert some tasks without assignments (unassigned tasks)
INSERT INTO tasks (title, description, created_by, deadline, status, priority) VALUES
('Unassigned High Priority Task', 'This task needs to be assigned to someone urgently', 1, NOW() + INTERVAL '2 days', 'pending', 4),
('Future Planning Task', 'Long-term planning task for next quarter', 2, NOW() + INTERVAL '60 days', 'pending', 1),
('Overdue Task Example', 'This task is overdue for testing purposes', 1, NOW() - INTERVAL '5 days', 'pending', 3);

-- Final data verification queries (commented out - uncomment to run)
/*
-- Verify the setup
SELECT 'Users created:' as info, COUNT(*) as count FROM users
UNION ALL
SELECT 'Tasks created:', COUNT(*) FROM tasks
UNION ALL
SELECT 'Task assignments:', COUNT(*) FROM task_assigned_users
UNION ALL
SELECT 'Roles created:', COUNT(*) FROM roles;

-- Show user summary
SELECT * FROM user_task_summary;

-- Show task details
SELECT task_id, title, status, priority, assigned_to_names, assignee_count
FROM task_details_view
ORDER BY priority DESC, deadline ASC NULLS LAST;

-- Show priority statistics
SELECT * FROM get_priority_stats();

-- Show stats for a specific user (user_id = 4)
SELECT * FROM get_user_stats(4);
*/

-- Success message
SELECT 'Database setup completed successfully! You now have:' as message
UNION ALL SELECT '- 3 roles (admin, manager, user)'
UNION ALL SELECT '- 12 users with various roles and verification status'
UNION ALL SELECT '- 23 tasks with different priorities and statuses'
UNION ALL SELECT '- Realistic task assignments'
UNION ALL SELECT '- Useful views and functions for reporting'
UNION ALL SELECT '- Proper indexes for performance'
UNION ALL SELECT ''
UNION ALL SELECT 'Default login credentials:'
UNION ALL SELECT 'Admin: <EMAIL> / password123'
UNION ALL SELECT 'Manager: <EMAIL> / password123'
UNION ALL SELECT 'User: <EMAIL> / password123';
